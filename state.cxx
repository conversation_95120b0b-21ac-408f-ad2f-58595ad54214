#include "callid.hxx"

#ifdef WIN32
#include <winsock2.h>
#include "gnproto.h"
#include "gncompress.h"
#else
#include "protocol.h"
#include "binder.h"
#endif

namespace GNET
{

static GNET::Protocol::Type _state_GLoginServer[] = 
{
	PROTOCOL_RESPONSE,
	PROTOCOL_KEYREESTABLISH,
};

GNET::Protocol::Manager::Session::State state_GLoginServer(_state_GLoginServer,
						sizeof(_state_GLoginServer)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GResponseReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GResponseReceive(_state_GResponseReceive,
						sizeof(_state_GResponseReceive)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GKeyReestablished[] = 
{
	PROTOCOL_SELECTROLE,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GKeyReestablished(_state_GKeyReestablished,
						sizeof(_state_GKeyReestablished)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GVerifyMatrix[] = 
{
	PROTOCOL_MATRIXRESPONSE,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GVerifyMatrix(_state_GVerifyMatrix,
						sizeof(_state_GVerifyMatrix)/sizeof(GNET::Protocol::Type), 180);

static GNET::Protocol::Type _state_GKeyExchgSend[] = 
{
	PROTOCOL_KEYEXCHANGE,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GKeyExchgSend(_state_GKeyExchgSend,
						sizeof(_state_GKeyExchgSend)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GKeyExchgSucc[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GKeyExchgSucc(_state_GKeyExchgSucc,
						sizeof(_state_GKeyExchgSucc)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GRoleList[] = 
{
	PROTOCOL_ROLELIST,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GRoleList(_state_GRoleList,
						sizeof(_state_GRoleList)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GRoleList_OnGame[] = 
{
	PROTOCOL_ROLELIST,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_GAMEDATASEND,
	PROTOCOL_PLAYERBASEINFO,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GRoleList_OnGame(_state_GRoleList_OnGame,
						sizeof(_state_GRoleList_OnGame)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GRoleListReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GRoleListReceive(_state_GRoleListReceive,
						sizeof(_state_GRoleListReceive)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GSelectRoleServer[] = 
{
	PROTOCOL_CREATEROLE,
	PROTOCOL_DELETEROLE,
	PROTOCOL_SELECTROLE,
	PROTOCOL_UNDODELETEROLE,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_SETCUSTOMDATA,
	PROTOCOL_GETCUSTOMDATA,
	PROTOCOL_SETHELPSTATES,
	PROTOCOL_GETHELPSTATES,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
	PROTOCOL_WEBTRADEROLEPREPOST,
	PROTOCOL_WEBTRADEROLEPRECANCELPOST,
	PROTOCOL_WEBTRADEROLEGETDETAIL,
	PROTOCOL_GETPLAYERIDBYNAME,
	PROTOCOL_SSOGETTICKET,
};

GNET::Protocol::Manager::Session::State state_GSelectRoleServer(_state_GSelectRoleServer,
						sizeof(_state_GSelectRoleServer)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GSelectRoleReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
	PROTOCOL_CANCELWAITQUEUE,
	RPC_PLAYERPOSITIONRESETRQST,
};

GNET::Protocol::Manager::Session::State state_GSelectRoleReceive(_state_GSelectRoleReceive,
						sizeof(_state_GSelectRoleReceive)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GResetPlayerPosReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GResetPlayerPosReceive(_state_GResetPlayerPosReceive,
						sizeof(_state_GResetPlayerPosReceive)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GCreateRoleReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GCreateRoleReceive(_state_GCreateRoleReceive,
						sizeof(_state_GCreateRoleReceive)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GDeleteRoleReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GDeleteRoleReceive(_state_GDeleteRoleReceive,
						sizeof(_state_GDeleteRoleReceive)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GUndoDeleteRoleReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GUndoDeleteRoleReceive(_state_GUndoDeleteRoleReceive,
						sizeof(_state_GUndoDeleteRoleReceive)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GReadyGame[] = 
{
	PROTOCOL_ENTERWORLD,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
	PROTOCOL_SETHELPSTATES,
	PROTOCOL_GETHELPSTATES,
};

GNET::Protocol::Manager::Session::State state_GReadyGame(_state_GReadyGame,
						sizeof(_state_GReadyGame)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GDataExchgServer[] = 
{
	PROTOCOL_ROLELIST,
	PROTOCOL_GAMEDATASEND,
	PROTOCOL_PUBLICCHAT,
	PROTOCOL_PRIVATECHAT,
	PROTOCOL_FACTIONCHAT,
	PROTOCOL_FACTIONALLIANCECHAT,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_PLAYERBASEINFO,
	PROTOCOL_PLAYERBASEINFOCRC,
	PROTOCOL_GETPLAYERIDBYNAME,
	PROTOCOL_SETCUSTOMDATA,
	PROTOCOL_GETCUSTOMDATA,
	PROTOCOL_SETUICONFIG,
	PROTOCOL_GETUICONFIG,
	PROTOCOL_SETHELPSTATES,
	PROTOCOL_GETHELPSTATES,
	PROTOCOL_GETPLAYERBRIEFINFO,
	PROTOCOL_GMGETPLAYERCONSUMEINFO,
	PROTOCOL_COLLECTCLIENTMACHINEINFO,
	PROTOCOL_ADDFRIEND,
	PROTOCOL_ADDFRIEND_RE,
	PROTOCOL_ADDFRIENDREMARKS,
	PROTOCOL_GETFRIENDS,
	PROTOCOL_GETFRIENDS_RE,
	PROTOCOL_GETENEMYLIST,
	PROTOCOL_SETGROUPNAME,
	PROTOCOL_SETGROUPNAME_RE,
	PROTOCOL_SETFRIENDGROUP,
	PROTOCOL_SETFRIENDGROUP_RE,
	PROTOCOL_DELFRIEND,
	PROTOCOL_DELFRIEND_RE,
	PROTOCOL_FRIENDSTATUS,
	PROTOCOL_GETSAVEDMSG,
	PROTOCOL_GETSAVEDMSG_RE,
	RPC_ADDFRIENDRQST,
	PROTOCOL_CHATROOMCREATE,
	PROTOCOL_CHATROOMINVITE,
	PROTOCOL_CHATROOMINVITE_RE,
	PROTOCOL_CHATROOMJOIN,
	PROTOCOL_CHATROOMLEAVE,
	PROTOCOL_CHATROOMEXPEL,
	PROTOCOL_CHATROOMSPEAK,
	PROTOCOL_CHATROOMLIST,
	PROTOCOL_SENDAUMAIL,
	PROTOCOL_PLAYERREQUITEFRIEND,
	PROTOCOL_GETSAVEDMSG2,
	PROTOCOL_TRADESTART,
	PROTOCOL_TRADEADDGOODS,
	PROTOCOL_TRADEREMOVEGOODS,
	PROTOCOL_TRADESUBMIT,
	PROTOCOL_TRADEMOVEOBJ,
	PROTOCOL_TRADECONFIRM,
	PROTOCOL_TRADEDISCARD,
	RPC_TRADESTARTRQST,
	PROTOCOL_FACTIONOPREQUEST,
	PROTOCOL_FACTIONACCEPTJOIN,
	PROTOCOL_GETFACTIONBASEINFO,
	PROTOCOL_GETPLAYERFACTIONINFO,
	PROTOCOL_BATTLEGETMAP,
	PROTOCOL_BATTLESTATUS,
	RPC_FACTIONINVITEJOIN,
	PROTOCOL_FACTIONLISTONLINE,
	PROTOCOL_HOMEBROWSEINFOQUERY,
	PROTOCOL_HOMEQUERY,
	PROTOCOL_HOMEEDITEND,
	PROTOCOL_HOMEFOUNDRYQUERY,
	PROTOCOL_HOMEVISITORQUERY,
	PROTOCOL_HOMEFOUNDRYFACTORYPRODUCEREQUEST,
	PROTOCOL_GMONLINENUM,
	PROTOCOL_GMLISTONLINEUSER,
	PROTOCOL_GMKICKOUTUSER,
	PROTOCOL_ACKICKOUTUSER,
	PROTOCOL_GMFORBIDSELLPOINT,
	PROTOCOL_GMKICKOUTROLE,
	PROTOCOL_GMSHUTUP,
	PROTOCOL_GMSHUTUPROLE,
	PROTOCOL_GMTOGGLECHAT,
	PROTOCOL_GMFORBIDROLE,
	PROTOCOL_GMRESTARTSERVER,
	PROTOCOL_REPORT2GM,
	PROTOCOL_COMPLAIN2GM,
	PROTOCOL_GMSETTIMELESSAUTOLOCK,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
	PROTOCOL_CHECKNEWMAIL,
	PROTOCOL_SELLPOINT,
	PROTOCOL_BUYPOINT,
	PROTOCOL_GETSELLLIST,
	PROTOCOL_FINDSELLPOINTINFO,
	PROTOCOL_SELLCANCEL,
	PROTOCOL_CASHLOCK,
	PROTOCOL_CASHPASSWORDSET,
	PROTOCOL_AUTOLOCKSET,
	PROTOCOL_REFWITHDRAWBONUS,
	PROTOCOL_REFLISTREFERRALS,
	PROTOCOL_REFGETREFERENCECODE,
	PROTOCOL_EXCHANGECONSUMEPOINTS,
	PROTOCOL_GETREWARDLIST,
	PROTOCOL_USERCOUPON,
	PROTOCOL_USERCOUPONEXCHANGE,
	PROTOCOL_USERADDCASH,
	PROTOCOL_SSOGETTICKET,
	PROTOCOL_COUNTRYBATTLEMOVE,
	PROTOCOL_COUNTRYBATTLEGETMAP,
	PROTOCOL_COUNTRYBATTLEGETPLAYERLOCATION,
	PROTOCOL_COUNTRYBATTLEGETCONFIG,
	PROTOCOL_COUNTRYBATTLEGETSCORE,
	PROTOCOL_COUNTRYBATTLEPREENTER,
	PROTOCOL_COUNTRYBATTLERETURNCAPITAL,
	PROTOCOL_COUNTRYBATTLEKINGASSIGNASSAULT,
	PROTOCOL_COUNTRYBATTLEKINGRESETBATTLELIMIT,
	PROTOCOL_COUNTRYBATTLEGETBATTLELIMIT,
	PROTOCOL_COUNTRYBATTLEGETKINGCOMMANDPOINT,
	PROTOCOL_GETCNETSERVERCONFIG,
	PROTOCOL_QPGETACTIVATEDSERVICES,
	PROTOCOL_QPADDCASH,
	PROTOCOL_REPORTCHAT,
	PROTOCOL_PLAYERACCUSE,
	PROTOCOL_PSHOPPLAYERGET,
	PROTOCOL_PSHOPLIST,
	PROTOCOL_PSHOPLISTITEM,
	PROTOCOL_PLAYERPROFILEGETPROFILEDATA,
	PROTOCOL_PLAYERPROFILESETPROFILEDATA,
	PROTOCOL_PLAYERPROFILEGETMATCHRESULT,
	PROTOCOL_TANKBATTLEPLAYERGETRANK,
	PROTOCOL_FACTIONRESOURCEBATTLEGETMAP,
	PROTOCOL_FACTIONRESOURCEBATTLEGETRECORD,
	PROTOCOL_MNGETPLAYERLASTENTERINFO,
	PROTOCOL_MNGETFACTIONBRIEFINFO,
	PROTOCOL_MNGETFACTIONINFO,
	PROTOCOL_EC_ARENAQUERY,
	PROTOCOL_EC_ARENAPLAYERTOTALINFOQUERY,
	PROTOCOL_EC_LDBATTLECLIENTOPERATE,
	PROTOCOL_EC_FACTIONSIGNALREQUEST,
	PROTOCOL_EC_SYSTEMMONITORLOG,
	PROTOCOL_EC_CROSSMATCHACTCOUNT,
	PROTOCOL_EC_SPOUSEQUERY,
};

GNET::Protocol::Manager::Session::State state_GDataExchgServer(_state_GDataExchgServer,
						sizeof(_state_GDataExchgServer)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GWebTradeOpReceive[] = 
{
	PROTOCOL_KEEPALIVE,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
};

GNET::Protocol::Manager::Session::State state_GWebTradeOpReceive(_state_GWebTradeOpReceive,
						sizeof(_state_GWebTradeOpReceive)/sizeof(GNET::Protocol::Type), 60);

static GNET::Protocol::Type _state_GPhoneReceive[] = 
{
	PROTOCOL_ROLELIST,
	PROTOCOL_GAMEDATASEND,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_FACTIONCHAT,
	PROTOCOL_FACTIONALLIANCECHAT,
	PROTOCOL_PUBLICCHAT,
	PROTOCOL_PRIVATECHAT,
	PROTOCOL_PLAYERBASEINFO,
	PROTOCOL_PLAYERBASEINFOCRC,
	PROTOCOL_GETFACTIONBASEINFO,
	PROTOCOL_GETPLAYERBRIEFINFO,
	PROTOCOL_GETPLAYERFACTIONINFO,
	PROTOCOL_FACTIONOPREQUEST,
	PROTOCOL_FACTIONACCEPTJOIN,
	PROTOCOL_BATTLEGETMAP,
	PROTOCOL_BATTLESTATUS,
	RPC_FACTIONINVITEJOIN,
	PROTOCOL_FACTIONLISTONLINE,
	PROTOCOL_GETSAVEDMSG,
	PROTOCOL_GETPLAYERIDBYNAME,
	PROTOCOL_GETSAVEDMSG2,
	PROTOCOL_ADDFRIEND,
	PROTOCOL_ADDFRIEND_RE,
	PROTOCOL_GETFRIENDS,
	PROTOCOL_GETFRIENDS_RE,
	PROTOCOL_SETGROUPNAME,
	PROTOCOL_SETGROUPNAME_RE,
	PROTOCOL_SETFRIENDGROUP,
	PROTOCOL_SETFRIENDGROUP_RE,
	PROTOCOL_DELFRIEND,
	PROTOCOL_DELFRIEND_RE,
	PROTOCOL_FRIENDSTATUS,
	PROTOCOL_GETSAVEDMSG,
	PROTOCOL_GETSAVEDMSG_RE,
	RPC_ADDFRIENDRQST,
	PROTOCOL_CHATROOMCREATE,
	PROTOCOL_CHATROOMINVITE,
	PROTOCOL_CHATROOMINVITE_RE,
	PROTOCOL_CHATROOMJOIN,
	PROTOCOL_CHATROOMLEAVE,
	PROTOCOL_CHATROOMEXPEL,
	PROTOCOL_CHATROOMSPEAK,
	PROTOCOL_CHATROOMLIST,
	PROTOCOL_SSOGETTICKET,
	PROTOCOL_GETSAVEDMSG2,
};

GNET::Protocol::Manager::Session::State state_GPhoneReceive(_state_GPhoneReceive,
						sizeof(_state_GPhoneReceive)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GDeliverClient[] = 
{
	PROTOCOL_QUERYUSERPRIVILEGE_RE,
	PROTOCOL_QUERYUSERFORBID_RE,
	PROTOCOL_KICKOUTUSER,
	PROTOCOL_STATUSANNOUNCE,
	PROTOCOL_ONLINEANNOUNCE,
	PROTOCOL_UPDATEREMAINTIME,
	PROTOCOL_PLAYERLOGIN_RE,
	PROTOCOL_PLAYERLOGOUT,
	PROTOCOL_ROLELIST_RE,
	PROTOCOL_CREATEROLE_RE,
	PROTOCOL_DELETEROLE_RE,
	PROTOCOL_UNDODELETEROLE_RE,
	PROTOCOL_ACCOUNTLOGINRECORD,
	PROTOCOL_PLAYERBASEINFO_RE,
	PROTOCOL_PLAYERBASEINFOCRC_RE,
	PROTOCOL_SETCUSTOMDATA_RE,
	PROTOCOL_GETCUSTOMDATA_RE,
	PROTOCOL_GETPLAYERIDBYNAME_RE,
	PROTOCOL_SETUICONFIG_RE,
	PROTOCOL_GETUICONFIG_RE,
	PROTOCOL_SETHELPSTATES_RE,
	PROTOCOL_GETHELPSTATES_RE,
	PROTOCOL_GETPLAYERBRIEFINFO_RE,
	PROTOCOL_GMGETPLAYERCONSUMEINFO_RE,
	PROTOCOL_DISCONNECTPLAYER,
	PROTOCOL_WAITQUEUESTATENOTIFY,
	PROTOCOL_CANCELWAITQUEUE_RE,
	PROTOCOL_PRIVATECHAT,
	PROTOCOL_CHATBROADCAST,
	PROTOCOL_WORLDCHAT,
	PROTOCOL_CHATMULTICAST,
	PROTOCOL_CHATSINGLECAST,
	PROTOCOL_ROLESTATUSANNOUNCE,
	PROTOCOL_HWIDPLAYERDATA,
	PROTOCOL_PLAYERTELEPORT,
	PROTOCOL_PLAYERLUAINFO,
	PROTOCOL_ADDFRIEND_RE,
	PROTOCOL_ADDFRIENDREMARKS_RE,
	PROTOCOL_GETFRIENDS_RE,
	PROTOCOL_UPDATEENEMYLIST_RE,
	PROTOCOL_GETENEMYLIST_RE,
	PROTOCOL_SETGROUPNAME_RE,
	PROTOCOL_SETFRIENDGROUP_RE,
	PROTOCOL_DELFRIEND_RE,
	PROTOCOL_FRIENDSTATUS,
	RPC_ADDFRIENDRQST,
	PROTOCOL_GETSAVEDMSG_RE,
	PROTOCOL_CHATROOMCREATE_RE,
	PROTOCOL_CHATROOMINVITE,
	PROTOCOL_CHATROOMINVITE_RE,
	PROTOCOL_CHATROOMJOIN_RE,
	PROTOCOL_CHATROOMLEAVE,
	PROTOCOL_CHATROOMEXPEL,
	PROTOCOL_CHATROOMSPEAK,
	PROTOCOL_CHATROOMLIST_RE,
	PROTOCOL_FRIENDEXTLIST,
	PROTOCOL_SENDAUMAIL_RE,
	PROTOCOL_TRADESTART,
	PROTOCOL_TRADESTART_RE,
	PROTOCOL_TRADEADDGOODS_RE,
	PROTOCOL_TRADEREMOVEGOODS_RE,
	PROTOCOL_TRADESUBMIT_RE,
	PROTOCOL_TRADEMOVEOBJ_RE,
	PROTOCOL_TRADECONFIRM_RE,
	PROTOCOL_TRADEDISCARD_RE,
	PROTOCOL_TRADEEND,
	RPC_TRADESTARTRQST,
	RPC_USERLOGIN,
	RPC_USERLOGIN2,
	RPC_GQUERYPASSWD,
	RPC_PLAYERPOSITIONRESETRQST,
	PROTOCOL_GMRESTARTSERVER,
	PROTOCOL_GMRESTARTSERVER_RE,
	PROTOCOL_GMONLINENUM_RE,
	PROTOCOL_GMLISTONLINEUSER_RE,
	PROTOCOL_GMKICKOUTUSER_RE,
	PROTOCOL_GMFORBIDSELLPOINT_RE,
	PROTOCOL_GMKICKOUTROLE_RE,
	PROTOCOL_GMSHUTUP,
	PROTOCOL_GMSHUTUP_RE,
	PROTOCOL_GMSHUTUPROLE,
	PROTOCOL_GMSHUTUPROLE_RE,
	PROTOCOL_GMTOGGLECHAT,
	PROTOCOL_GMTOGGLECHAT_RE,
	PROTOCOL_GMFORBIDROLE,
	PROTOCOL_GMFORBIDROLE_RE,
	PROTOCOL_REPORT2GM_RE,
	PROTOCOL_COMPLAIN2GM_RE,
	PROTOCOL_ANNOUNCEFORBIDINFO,
	PROTOCOL_SETMAXONLINENUM_RE,
	PROTOCOL_GMCONTROLGAME_RE,
	PROTOCOL_GMSETTIMELESSAUTOLOCK_RE,
	PROTOCOL_ACREMOTECODE,
	PROTOCOL_ACQUESTION,
	PROTOCOL_PLAYERACCUSE_RE,
	PROTOCOL_ANNOUNCENEWMAIL,
	PROTOCOL_GETMAILLIST_RE,
	PROTOCOL_GETMAIL_RE,
	PROTOCOL_GETMAILATTACHOBJ_RE,
	PROTOCOL_DELETEMAIL_RE,
	PROTOCOL_PRESERVEMAIL_RE,
	PROTOCOL_PLAYERSENDMAIL_RE,
	PROTOCOL_AUCTIONOPEN_RE,
	PROTOCOL_AUCTIONBID_RE,
	PROTOCOL_AUCTIONCLOSE_RE,
	PROTOCOL_AUCTIONLIST_RE,
	PROTOCOL_AUCTIONGET_RE,
	PROTOCOL_AUCTIONGETITEM_RE,
	PROTOCOL_AUCTIONATTENDLIST_RE,
	PROTOCOL_AUCTIONEXITBID_RE,
	PROTOCOL_AUCTIONLISTUPDATE_RE,
	PROTOCOL_BATTLEGETMAP_RE,
	PROTOCOL_BATTLESTATUS_RE,
	PROTOCOL_BATTLECHALLENGE_RE,
	PROTOCOL_BATTLECHALLENGEMAP_RE,
	PROTOCOL_BATTLEENTER_RE,
	PROTOCOL_COUNTRYBATTLEMOVE_RE,
	PROTOCOL_COUNTRYBATTLESYNCPLAYERLOCATION,
	PROTOCOL_COUNTRYBATTLEGETMAP_RE,
	PROTOCOL_COUNTRYBATTLEGETCONFIG_RE,
	PROTOCOL_COUNTRYBATTLEGETSCORE_RE,
	PROTOCOL_COUNTRYBATTLEPREENTERNOTIFY,
	PROTOCOL_COUNTRYBATTLERESULT,
	PROTOCOL_COUNTRYBATTLESINGLEBATTLERESULT,
	PROTOCOL_COUNTRYBATTLEKINGASSIGNASSAULT_RE,
	PROTOCOL_COUNTRYBATTLEGETBATTLELIMIT_RE,
	PROTOCOL_COUNTRYBATTLEGETKINGCOMMANDPOINT_RE,
	PROTOCOL_GETCNETSERVERCONFIG_RE,
	PROTOCOL_HOMEBROWSEINFOQUERY_RE,
	PROTOCOL_HOMEVISITORINFO,
	PROTOCOL_HOMEEDITRES,
	PROTOCOL_HOMEFOUNDRYFORMULASSYNC,
	PROTOCOL_HOMESYNCNOTICECLIENT,
	PROTOCOL_HOMEDETAIL,
	PROTOCOL_HOMEBRIEF,
	PROTOCOL_ANNOUNCESERVERATTRIBUTE,
	PROTOCOL_ANNOUNCECHALLENGEALGO,
	PROTOCOL_ANNOUNCEAUTHDVERSION,
	PROTOCOL_GETSELLLIST_RE,
	PROTOCOL_FINDSELLPOINTINFO_RE,
	PROTOCOL_ANNOUNCESELLRESULT,
	PROTOCOL_SELLCANCEL_RE,
	PROTOCOL_BUYPOINT_RE,
	PROTOCOL_SELLPOINT_RE,
	RPC_DBSELLPOINT,
	RPC_DBBUYPOINT,
	PROTOCOL_STOCKCOMMISSION_RE,
	PROTOCOL_STOCKACCOUNT_RE,
	PROTOCOL_STOCKTRANSACTION_RE,
	PROTOCOL_STOCKBILL_RE,
	PROTOCOL_STOCKCANCEL_RE,
	PROTOCOL_CASHLOCK_RE,
	PROTOCOL_CASHPASSWORDSET_RE,
	RPC_MATRIXPASSWD,
	RPC_MATRIXPASSWD2,
	RPC_MATRIXTOKEN,
	PROTOCOL_AUTOLOCKSET_RE,
	PROTOCOL_FORWARDCHAT,
	PROTOCOL_DISABLEAUTOLOCK,
	PROTOCOL_REFWITHDRAWBONUS_RE,
	PROTOCOL_REFLISTREFERRALS_RE,
	PROTOCOL_REFGETREFERENCECODE_RE,
	PROTOCOL_REWARDMATURENOTICE,
	PROTOCOL_EXCHANGECONSUMEPOINTS_RE,
	PROTOCOL_GETREWARDLIST_RE,
	PROTOCOL_WEBTRADEPREPOST_RE,
	PROTOCOL_WEBTRADEPRECANCELPOST_RE,
	PROTOCOL_WEBTRADELIST_RE,
	PROTOCOL_WEBTRADEGETITEM_RE,
	PROTOCOL_WEBTRADEATTENDLIST_RE,
	PROTOCOL_WEBTRADEGETDETAIL_RE,
	PROTOCOL_WEBTRADEUPDATE_RE,
	PROTOCOL_SYSAUCTIONLIST_RE,
	PROTOCOL_SYSAUCTIONGETITEM_RE,
	PROTOCOL_SYSAUCTIONACCOUNT_RE,
	PROTOCOL_SYSAUCTIONBID_RE,
	PROTOCOL_SYSAUCTIONCASHTRANSFER_RE,
	PROTOCOL_CREATEFACTIONFORTRESS_RE,
	PROTOCOL_FACTIONFORTRESSLIST_RE,
	PROTOCOL_FACTIONFORTRESSCHALLENGE_RE,
	PROTOCOL_FACTIONFORTRESSBATTLELIST_RE,
	PROTOCOL_FACTIONFORTRESSGET_RE,
	PROTOCOL_SWITCHSERVERSTART,
	PROTOCOL_USERCOUPON_RE,
	PROTOCOL_USERCOUPONEXCHANGE_RE,
	PROTOCOL_USERADDCASH_RE,
	PROTOCOL_SSOGETTICKET_RE,
	PROTOCOL_QPANNOUNCEDISCOUNT,
	PROTOCOL_QPGETACTIVATEDSERVICES_RE,
	PROTOCOL_QPADDCASH_RE,
	PROTOCOL_PLAYERCHANGEDS_RE,
	PROTOCOL_CHANGEDS_RE,
	RPC_PLAYERIDENTITYMATCH,
	PROTOCOL_KICKOUTUSER2,
	PROTOCOL_PLAYERRENAME_RE,
	PROTOCOL_PLAYERNAMEUPDATE,
	PROTOCOL_GETSOLOCHALLENGERANK_RE,
	PROTOCOL_KEGETSTATUS_RE,
	PROTOCOL_KECANDIDATEAPPLY_RE,
	PROTOCOL_KEVOTING_RE,
	PROTOCOL_PSHOPCREATE_RE,
	PROTOCOL_PSHOPBUY_RE,
	PROTOCOL_PSHOPSELL_RE,
	PROTOCOL_PSHOPCANCELGOODS_RE,
	PROTOCOL_PSHOPPLAYERBUY_RE,
	PROTOCOL_PSHOPPLAYERSELL_RE,
	PROTOCOL_PSHOPSETTYPE_RE,
	PROTOCOL_PSHOPACTIVE_RE,
	PROTOCOL_PSHOPMANAGEFUND_RE,
	PROTOCOL_PSHOPDRAWITEM_RE,
	PROTOCOL_PSHOPCLEARGOODS_RE,
	PROTOCOL_PSHOPSELFGET_RE,
	PROTOCOL_PSHOPPLAYERGET_RE,
	PROTOCOL_PSHOPLIST_RE,
	PROTOCOL_PSHOPLISTITEM_RE,
	PROTOCOL_PLAYERGIVEPRESENT_RE,
	PROTOCOL_PLAYERASKFORPRESENT_RE,
	PROTOCOL_PLAYERPROFILEGETPROFILEDATA_RE,
	PROTOCOL_PLAYERPROFILEGETMATCHRESULT_RE,
	PROTOCOL_UNIQUEDATAMODIFYBROADCAST,
	PROTOCOL_TANKBATTLEPLAYERAPPLY_RE,
	PROTOCOL_TANKBATTLEPLAYERGETRANK_RE,
	PROTOCOL_AUTOTEAMSETGOAL_RE,
	PROTOCOL_AUTOTEAMPLAYERLEAVE,
	PROTOCOL_FACTIONRESOURCEBATTLEPLAYERQUERYRESULT,
	PROTOCOL_FACTIONRESOURCEBATTLEGETMAP_RE,
	PROTOCOL_FACTIONRESOURCEBATTLEGETRECORD_RE,
	PROTOCOL_FACTIONRESOURCEBATTLENOTIFYPLAYEREVENT,
	PROTOCOL_MNFACTIONBATTLEAPPLY_RE,
	PROTOCOL_MNGETDOMAINDATA_RE,
	PROTOCOL_MNGETPLAYERLASTENTERINFO_RE,
	PROTOCOL_MNGETFACTIONBRIEFINFO_RE,
	PROTOCOL_MNGETFACTIONINFO_RE,
	PROTOCOL_MNGETTOPLIST_RE,
	PROTOCOL_EC_ARENAPLAYERTOPLISTQUERY_RE,
	PROTOCOL_EC_ARENATEAMTOPLISTQUERY_RE,
	PROTOCOL_EC_ARENATEAMTOPLISTDETAILQUERY_RE,
	PROTOCOL_EC_ARENAQUERY_RE,
	PROTOCOL_EC_ARENAPLAYERTOTALINFOQUERY_RE,
	PROTOCOL_EC_ARENATEAMDATANOTIFY,
	PROTOCOL_EC_ARENAPLAYERDATANOTIFY,
	PROTOCOL_EC_ARENATEAMCREATE,
	PROTOCOL_EC_ARENABATTLEINFONOTIFYCLIENT,
	PROTOCOL_EC_ARENACLIENTNOTIFY,
	PROTOCOL_EC_ARENATEAMMATCH,
	PROTOCOL_EC_ARENAMATCHCANCELNOTIFY,
	PROTOCOL_EC_ARENAONLINEINFONOTIFY,
	PROTOCOL_GCODEXREQUESTSTORAGE,
	PROTOCOL_GCODEXREQUESTSTORAGE_RE,
};

GNET::Protocol::Manager::Session::State state_GDeliverClient(_state_GDeliverClient,
						sizeof(_state_GDeliverClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GProviderLinkServer[] = 
{
	PROTOCOL_ANNOUNCEPROVIDERID,
	PROTOCOL_S2CGAMEDATASEND,
	PROTOCOL_S2CMULTICAST,
	PROTOCOL_S2CBROADCAST,
	PROTOCOL_CHATBROADCAST,
	PROTOCOL_CHATMULTICAST,
	PROTOCOL_CHATSINGLECAST,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_PLAYERHEARTBEAT,
	PROTOCOL_DISCONNECTPLAYER,
	PROTOCOL_SWITCHSERVERCANCEL,
	PROTOCOL_SWITCHSERVERSUCCESS,
	PROTOCOL_SWITCHSERVERTIMEOUT,
};

GNET::Protocol::Manager::Session::State state_GProviderLinkServer(_state_GProviderLinkServer,
						sizeof(_state_GProviderLinkServer)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GFactionLinkClient[] = 
{
	PROTOCOL_FACTIONOPREQUEST_RE,
	PROTOCOL_FACTIONCREATE_RE,
	PROTOCOL_FACTIONAPPLYJOIN_RE,
	PROTOCOL_FACTIONLISTMEMBER_RE,
	PROTOCOL_FACTIONACCEPTJOIN_RE,
	PROTOCOL_FACTIONEXPEL_RE,
	PROTOCOL_FACTIONBROADCASTNOTICE_RE,
	PROTOCOL_FACTIONMASTERRESIGN_RE,
	PROTOCOL_FACTIONAPPOINT_RE,
	PROTOCOL_FACTIONRESIGN_RE,
	PROTOCOL_FACTIONCHANGPROCLAIM_RE,
	PROTOCOL_FACTIONLEAVE_RE,
	PROTOCOL_FACTIONDISMISS_RE,
	PROTOCOL_FACTIONRENAME_RE,
	PROTOCOL_FACTIONUPGRADE_RE,
	PROTOCOL_FACTIONDEGRADE_RE,
	RPC_FACTIONINVITEJOIN,
	PROTOCOL_FACTIONALLIANCEAPPLY_RE,
	PROTOCOL_FACTIONALLIANCEREPLY_RE,
	PROTOCOL_FACTIONHOSTILEAPPLY_RE,
	PROTOCOL_FACTIONHOSTILEREPLY_RE,
	PROTOCOL_FACTIONREMOVERELATIONAPPLY_RE,
	PROTOCOL_FACTIONREMOVERELATIONREPLY_RE,
	PROTOCOL_FACTIONLISTRELATION_RE,
	PROTOCOL_FACTIONRELATIONRECVAPPLY,
	PROTOCOL_FACTIONRELATIONRECVREPLY,
	PROTOCOL_FACTIONCHAT,
	PROTOCOL_FACTIONALLIANCECHAT,
	PROTOCOL_FACTIONDELAYEXPELANNOUNCE,
	PROTOCOL_GETFACTIONBASEINFO_RE,
	PROTOCOL_GETPLAYERFACTIONINFO_RE,
	PROTOCOL_FACTIONLISTONLINE_RE,
	PROTOCOL_BATTLEENTER_RE,
	PROTOCOL_WORLDCHAT,
	PROTOCOL_BATTLECHALLENGEMAP_RE,
	PROTOCOL_PLAYERNAMEUPDATE,
	PROTOCOL_FACTIONRENAMEANNOUNCE,
};

GNET::Protocol::Manager::Session::State state_GFactionLinkClient(_state_GFactionLinkClient,
						sizeof(_state_GFactionLinkClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state___only_for_stub[] = 
{
	PROTOCOL_BINDER,
	PROTOCOL_COMPRESSBINDER,
};

GNET::Protocol::Manager::Session::State state___only_for_stub(_state___only_for_stub,
						sizeof(_state___only_for_stub)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_Null[] = 
{
};

GNET::Protocol::Manager::Session::State state_Null(_state_Null,
						sizeof(_state_Null)/sizeof(GNET::Protocol::Type), 5);


};


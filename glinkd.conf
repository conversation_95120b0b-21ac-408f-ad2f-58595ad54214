[LogclientClient]
type            =   udp
port            =   11100
;address            =   **************
address         =   ************
so_sndbuf       =   16777216
so_rcvbuf       =   16384
accumulate      =   16777216

[LogclientTcpClient]
type            =   tcp
port            =   11101
;address            =   **************
address         =   ************
so_sndbuf       =   16777216
so_rcvbuf       =   16384
accumulate      =   16777216

[ProviderServerID]
127.0.0.1	=	0
************	=	1
************	=	2
************	=	3
************	=	4
************	=	5
************	=	6
************	=	7
************	=	8
************	=	9
************	=	10

GTEST			=	1
gs01			=	2
gs02			=	3
gs03			=	4
gs04			=	5
gs05			=	6
gs06			=	7
gs07			=	8
gs08			=	9
gs09			=	10

[GAntiCheatClient]
type			=	tcp
port			=	29700
address			=	************
so_sndbuf		=	1638400
so_rcvbuf		=	1638400
ibuffermax		=	1638400
obuffermax		=	1638400
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	268435456

[GLinkServer1]
type			=	tcp
port			=	9000
address			=	0.0.0.0
so_sndbuf		=	16384
so_rcvbuf		=	16384
ibuffermax		=	16384
obuffermax		=	65535
tcp_nodelay		=	0
listen_backlog	=	10
accumulate		=	131072
max_users		=	3000
max_conns		=	3192
halflogin_users =	20
sender_interval	=	100000
accumu_packets	=	4000
mtrace			=	/tmp/m_trace.link
urgency_support =   1
;isec			=	2
;iseckey		=	123
;osec			=	2
;oseckey		=	456
compress		=	0
version			=	804

[GDeliveryClient]
type			=	tcp
port			=	9100
address			=	************
so_sndbuf		=	16384
so_rcvbuf		=	16384
ibuffermax		=	16384
obuffermax		=	16384
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	268435456
;isec			=	2
;iseckey		=	123
;osec			=	2
;oseckey		=	456

[GFactionClient]
type			=	tcp
port			=	9500
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	268435456
;isec			=	2
;iseckey		=	123
;osec			=	2
;oseckey		=	456

[GProviderServer1]
;linkid			=	1
type			=	tcp
port			=	9301
address			=	0.0.0.0
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	268435456

[storage]
homedir				=	./dbhome
datadir				=	dbdata
logdir				=	dblogs
backupdir			=	./backup
cachesize			=	16777216
errpfx				=	Storage
checkpoint_interval	=	5
backup_interval		=	60

[ThreadPool]
threads				=	(1,2)(100,1)(101,1)(0,1)
max_queuesize		=	1048576
prior_strict		=	1



#ifndef __GNET_PLAYERPOSITIONRESET_HPP
#define __GNET_PLAYERPOSITIONRESET_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#include "playerpositionresetrqstarg"
#include "playerpositionresetrqstres"
#include "glinkserver.hpp"

namespace GNET
{

class PlayerPositionResetRqst : public ProxyRpc
{
#define	RPC_BASECLASS	ProxyRpc
	#include "playerpositionresetrqst"
#undef	RPC_BASECLASS

	bool Delivery(Manager::Session::ID proxy_sid, const OctetsStream& osArg)
	{
		// TODO
		PlayerPositionResetRqstArg arg;
		osArg >> arg;
		if( GLinkServer::GetInstance()->Send(arg.localsid, this ) )
		{
			DEBUG_PRINT("glinkd::playerpositionresetrqst:change localsidsid%d state\n",arg.localsid);
			return true;
		}
		else
		{
			DEBUG_PRINT("glinkd::playerpositionresetrqst:cant find localsidsid%d \n",arg.localsid);
	
			SetResult(PlayerPositionResetRqstRes(0));
			SendToSponsor();
			return false;
		}
	}

	void PostProcess(Manager::Session::ID proxy_sid,const OctetsStream& osArg, const OctetsStream& osRes)
	{
		// TODO
		PlayerPositionResetRqstArg arg;
		osArg >> arg;
		GLinkServer::GetInstance()->ChangeState(arg.localsid,&state_GResetPlayerPosReceive);

	}

	void OnTimeout( )
	{
		// TODO Client Only
	}

};

};
#endif

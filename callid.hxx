
#ifndef __GNET_GLINKD_CALLID
#define __GNET_GLINKD_CALLID

namespace GNET
{

enum CallID
{
	RPC_ADDFRIENDRQST	=	204,
	RPC_TRADESTARTRQST	=	4003,
	RPC_FACTIONINVITEJOIN	=	4813,
	R<PERSON>_PLAYERPOSITIONRESETRQST	=	155,
	RPC_USERLOGIN	=	15,
	RPC_USERLOGIN2	=	8067,
	RPC_GQUERYPASSWD	=	502,
	RPC_DBSELLPOINT	=	611,
	RPC_DBBUYPOINT	=	615,
	RPC_MATRIXPASSWD	=	550,
	RPC_MATRIXPASSWD2	=	8066,
	RPC_MATRIXTOKEN	=	8070,
	RPC_PLAYERIDENTITYMATCH	=	1113,
};

enum ProtocolType
{
	PROTOCOL_AUCTIONEXITBID	=	818,
	PROTOCOL_ERRORINFO	=	5,
	PROTOCOL_CHALLENGE	=	1,
	PROTOCOL_SELECTROLE_RE	=	71,
	PROTOCOL_PLAYERLOGIN	=	65,
	PROTOCOL_PLAYEROFFLINE	=	67,
	PROTOCOL_PLAYERSTATUSSYNC	=	95,
	PROTOCOL_C2SGAMEDATASEND	=	75,
	PROTOCOL_CHATMESSAGE	=	80,
	PROTOCOL_WORLDCHAT	=	133,
	PROTOCOL_ANNOUNCENEWMAIL	=	4201,
	PROTOCOL_ACWHOAMI	=	5002,
	PROTOCOL_ACPROTOSTAT	=	5024,
	PROTOCOL_REPORTIP	=	35,
	PROTOCOL_SELLPOINT_RE	=	602,
	PROTOCOL_MATRIXCHALLENGE	=	551,
	PROTOCOL_MATRIXFAILURE	=	553,
	PROTOCOL_ANNOUNCELINKVERSION	=	1130,
	PROTOCOL_PLAYERRENAME_RE	=	3060,
	PROTOCOL_PLAYERNAMEUPDATE	=	158,
	PROTOCOL_GETSOLOCHALLENGERANK_RE	=	3076,
	PROTOCOL_UPDATEENEMYLIST_RE	=	239,
	PROTOCOL_PLAYERGIVEPRESENT_RE	=	3066,
	PROTOCOL_PLAYERASKFORPRESENT_RE	=	3069,
	PROTOCOL_ANNOUNCELINKTYPE	=	372,
	PROTOCOL_TANKBATTLEPLAYERAPPLY_RE	=	4873,
	PROTOCOL_TANKBATTLEPLAYERGETRANK	=	4881,
	PROTOCOL_TANKBATTLEPLAYERGETRANK_RE	=	4882,
	PROTOCOL_UNIQUEDATAMODIFYBROADCAST	=	3118,
	PROTOCOL_MNFACTIONBATTLEAPPLY_RE	=	5212,
	PROTOCOL_MNGETDOMAINDATA_RE	=	5241,
	PROTOCOL_MNGETPLAYERLASTENTERINFO	=	5242,
	PROTOCOL_MNGETPLAYERLASTENTERINFO_RE	=	5243,
	PROTOCOL_MNGETFACTIONBRIEFINFO	=	5244,
	PROTOCOL_MNGETFACTIONBRIEFINFO_RE	=	5245,
	PROTOCOL_MNGETTOPLIST_RE	=	5247,
	PROTOCOL_RESPONSE	=	3,
	PROTOCOL_KEYREESTABLISH	=	1105,
	PROTOCOL_KEEPALIVE	=	90,
	PROTOCOL_ACREPORT	=	5001,
	PROTOCOL_ACANSWER	=	5032,
	PROTOCOL_SELECTROLE	=	70,
	PROTOCOL_MATRIXRESPONSE	=	552,
	PROTOCOL_KEYEXCHANGE	=	2,
	PROTOCOL_ROLELIST	=	82,
	PROTOCOL_GAMEDATASEND	=	34,
	PROTOCOL_PLAYERBASEINFO	=	91,
	PROTOCOL_CREATEROLE	=	84,
	PROTOCOL_DELETEROLE	=	86,
	PROTOCOL_UNDODELETEROLE	=	88,
	PROTOCOL_SETCUSTOMDATA	=	100,
	PROTOCOL_GETCUSTOMDATA	=	116,
	PROTOCOL_SETHELPSTATES	=	128,
	PROTOCOL_GETHELPSTATES	=	130,
	PROTOCOL_WEBTRADEROLEPREPOST	=	4326,
	PROTOCOL_WEBTRADEROLEPRECANCELPOST	=	4328,
	PROTOCOL_WEBTRADEROLEGETDETAIL	=	4329,
	PROTOCOL_GETPLAYERIDBYNAME	=	118,
	PROTOCOL_SSOGETTICKET	=	147,
	PROTOCOL_CANCELWAITQUEUE	=	165,
	PROTOCOL_ENTERWORLD	=	72,
	PROTOCOL_PUBLICCHAT	=	79,
	PROTOCOL_PRIVATECHAT	=	96,
	PROTOCOL_FACTIONCHAT	=	4803,
	PROTOCOL_FACTIONALLIANCECHAT	=	4825,
	PROTOCOL_PLAYERBASEINFOCRC	=	98,
	PROTOCOL_SETUICONFIG	=	102,
	PROTOCOL_GETUICONFIG	=	104,
	PROTOCOL_GETPLAYERBRIEFINFO	=	107,
	PROTOCOL_GMGETPLAYERCONSUMEINFO	=	382,
	PROTOCOL_COLLECTCLIENTMACHINEINFO	=	37,
	PROTOCOL_ADDFRIEND	=	202,
	PROTOCOL_ADDFRIEND_RE	=	203,
	PROTOCOL_ADDFRIENDREMARKS	=	236,
	PROTOCOL_GETFRIENDS	=	206,
	PROTOCOL_GETFRIENDS_RE	=	207,
	PROTOCOL_GETENEMYLIST	=	240,
	PROTOCOL_SETGROUPNAME	=	208,
	PROTOCOL_SETGROUPNAME_RE	=	209,
	PROTOCOL_SETFRIENDGROUP	=	210,
	PROTOCOL_SETFRIENDGROUP_RE	=	211,
	PROTOCOL_DELFRIEND	=	212,
	PROTOCOL_DELFRIEND_RE	=	213,
	PROTOCOL_FRIENDSTATUS	=	214,
	PROTOCOL_GETSAVEDMSG	=	217,
	PROTOCOL_GETSAVEDMSG_RE	=	218,
	PROTOCOL_CHATROOMCREATE	=	219,
	PROTOCOL_CHATROOMINVITE	=	221,
	PROTOCOL_CHATROOMINVITE_RE	=	222,
	PROTOCOL_CHATROOMJOIN	=	223,
	PROTOCOL_CHATROOMLEAVE	=	225,
	PROTOCOL_CHATROOMEXPEL	=	226,
	PROTOCOL_CHATROOMSPEAK	=	227,
	PROTOCOL_CHATROOMLIST	=	228,
	PROTOCOL_SENDAUMAIL	=	233,
	PROTOCOL_PLAYERREQUITEFRIEND	=	3072,
	PROTOCOL_GETSAVEDMSG2	=	244,
	PROTOCOL_TRADESTART	=	4001,
	PROTOCOL_TRADEADDGOODS	=	4004,
	PROTOCOL_TRADEREMOVEGOODS	=	4006,
	PROTOCOL_TRADESUBMIT	=	4010,
	PROTOCOL_TRADEMOVEOBJ	=	4008,
	PROTOCOL_TRADECONFIRM	=	4012,
	PROTOCOL_TRADEDISCARD	=	4014,
	PROTOCOL_FACTIONOPREQUEST	=	4804,
	PROTOCOL_FACTIONACCEPTJOIN	=	4812,
	PROTOCOL_GETFACTIONBASEINFO	=	4814,
	PROTOCOL_GETPLAYERFACTIONINFO	=	4816,
	PROTOCOL_BATTLEGETMAP	=	850,
	PROTOCOL_BATTLESTATUS	=	866,
	PROTOCOL_FACTIONLISTONLINE	=	4819,
	PROTOCOL_HOMEBROWSEINFOQUERY	=	5399,
	PROTOCOL_HOMEQUERY	=	5356,
	PROTOCOL_HOMEEDITEND	=	5361,
	PROTOCOL_HOMEFOUNDRYQUERY	=	5392,
	PROTOCOL_HOMEVISITORQUERY	=	5397,
	PROTOCOL_HOMEFOUNDRYFACTORYPRODUCEREQUEST	=	5391,
	PROTOCOL_GMONLINENUM	=	350,
	PROTOCOL_GMLISTONLINEUSER	=	352,
	PROTOCOL_GMKICKOUTUSER	=	354,
	PROTOCOL_ACKICKOUTUSER	=	5035,
	PROTOCOL_GMFORBIDSELLPOINT	=	378,
	PROTOCOL_GMKICKOUTROLE	=	360,
	PROTOCOL_GMSHUTUP	=	356,
	PROTOCOL_GMSHUTUPROLE	=	362,
	PROTOCOL_GMTOGGLECHAT	=	364,
	PROTOCOL_GMFORBIDROLE	=	366,
	PROTOCOL_GMRESTARTSERVER	=	358,
	PROTOCOL_REPORT2GM	=	368,
	PROTOCOL_COMPLAIN2GM	=	370,
	PROTOCOL_GMSETTIMELESSAUTOLOCK	=	385,
	PROTOCOL_CHECKNEWMAIL	=	4200,
	PROTOCOL_SELLPOINT	=	601,
	PROTOCOL_BUYPOINT	=	607,
	PROTOCOL_GETSELLLIST	=	603,
	PROTOCOL_FINDSELLPOINTINFO	=	619,
	PROTOCOL_SELLCANCEL	=	605,
	PROTOCOL_CASHLOCK	=	4260,
	PROTOCOL_CASHPASSWORDSET	=	4263,
	PROTOCOL_AUTOLOCKSET	=	782,
	PROTOCOL_REFWITHDRAWBONUS	=	4906,
	PROTOCOL_REFLISTREFERRALS	=	4904,
	PROTOCOL_REFGETREFERENCECODE	=	4908,
	PROTOCOL_EXCHANGECONSUMEPOINTS	=	4954,
	PROTOCOL_GETREWARDLIST	=	4952,
	PROTOCOL_USERCOUPON	=	138,
	PROTOCOL_USERCOUPONEXCHANGE	=	140,
	PROTOCOL_USERADDCASH	=	144,
	PROTOCOL_COUNTRYBATTLEMOVE	=	4760,
	PROTOCOL_COUNTRYBATTLEGETMAP	=	4767,
	PROTOCOL_COUNTRYBATTLEGETPLAYERLOCATION	=	4770,
	PROTOCOL_COUNTRYBATTLEGETCONFIG	=	4772,
	PROTOCOL_COUNTRYBATTLEGETSCORE	=	4774,
	PROTOCOL_COUNTRYBATTLEPREENTER	=	4777,
	PROTOCOL_COUNTRYBATTLERETURNCAPITAL	=	4779,
	PROTOCOL_COUNTRYBATTLEKINGASSIGNASSAULT	=	4781,
	PROTOCOL_COUNTRYBATTLEKINGRESETBATTLELIMIT	=	4783,
	PROTOCOL_COUNTRYBATTLEGETBATTLELIMIT	=	4784,
	PROTOCOL_COUNTRYBATTLEGETKINGCOMMANDPOINT	=	4786,
	PROTOCOL_GETCNETSERVERCONFIG	=	4788,
	PROTOCOL_QPGETACTIVATEDSERVICES	=	150,
	PROTOCOL_QPADDCASH	=	152,
	PROTOCOL_REPORTCHAT	=	156,
	PROTOCOL_PLAYERACCUSE	=	161,
	PROTOCOL_PSHOPPLAYERGET	=	924,
	PROTOCOL_PSHOPLIST	=	926,
	PROTOCOL_PSHOPLISTITEM	=	928,
	PROTOCOL_PLAYERPROFILEGETPROFILEDATA	=	951,
	PROTOCOL_PLAYERPROFILESETPROFILEDATA	=	953,
	PROTOCOL_PLAYERPROFILEGETMATCHRESULT	=	954,
	PROTOCOL_FACTIONRESOURCEBATTLEGETMAP	=	4434,
	PROTOCOL_FACTIONRESOURCEBATTLEGETRECORD	=	4436,
	PROTOCOL_MNGETFACTIONINFO	=	5248,
	PROTOCOL_EC_ARENAQUERY	=	5621,
	PROTOCOL_EC_ARENAPLAYERTOTALINFOQUERY	=	5664,
	PROTOCOL_EC_LDBATTLECLIENTOPERATE	=	5291,
	PROTOCOL_EC_FACTIONSIGNALREQUEST	=	5898,
	PROTOCOL_EC_SYSTEMMONITORLOG	=	5813,
	PROTOCOL_EC_CROSSMATCHACTCOUNT	=	6213,
	PROTOCOL_EC_SPOUSEQUERY	=	6108,
	PROTOCOL_QUERYUSERPRIVILEGE_RE	=	507,
	PROTOCOL_QUERYUSERFORBID_RE	=	509,
	PROTOCOL_KICKOUTUSER	=	10,
	PROTOCOL_STATUSANNOUNCE	=	6,
	PROTOCOL_ONLINEANNOUNCE	=	4,
	PROTOCOL_UPDATEREMAINTIME	=	36,
	PROTOCOL_PLAYERLOGIN_RE	=	66,
	PROTOCOL_PLAYERLOGOUT	=	69,
	PROTOCOL_ROLELIST_RE	=	83,
	PROTOCOL_CREATEROLE_RE	=	85,
	PROTOCOL_DELETEROLE_RE	=	87,
	PROTOCOL_UNDODELETEROLE_RE	=	89,
	PROTOCOL_ACCOUNTLOGINRECORD	=	143,
	PROTOCOL_PLAYERBASEINFO_RE	=	92,
	PROTOCOL_PLAYERBASEINFOCRC_RE	=	99,
	PROTOCOL_SETCUSTOMDATA_RE	=	101,
	PROTOCOL_GETCUSTOMDATA_RE	=	117,
	PROTOCOL_GETPLAYERIDBYNAME_RE	=	119,
	PROTOCOL_SETUICONFIG_RE	=	103,
	PROTOCOL_GETUICONFIG_RE	=	105,
	PROTOCOL_SETHELPSTATES_RE	=	129,
	PROTOCOL_GETHELPSTATES_RE	=	131,
	PROTOCOL_GETPLAYERBRIEFINFO_RE	=	108,
	PROTOCOL_GMGETPLAYERCONSUMEINFO_RE	=	383,
	PROTOCOL_DISCONNECTPLAYER	=	106,
	PROTOCOL_WAITQUEUESTATENOTIFY	=	164,
	PROTOCOL_CANCELWAITQUEUE_RE	=	166,
	PROTOCOL_CHATBROADCAST	=	120,
	PROTOCOL_CHATMULTICAST	=	81,
	PROTOCOL_CHATSINGLECAST	=	94,
	PROTOCOL_ROLESTATUSANNOUNCE	=	7,
	PROTOCOL_HWIDPLAYERDATA	=	13009,
	PROTOCOL_PLAYERTELEPORT	=	13010,
	PROTOCOL_PLAYERLUAINFO	=	13011,
	PROTOCOL_ADDFRIENDREMARKS_RE	=	237,
	PROTOCOL_GETENEMYLIST_RE	=	241,
	PROTOCOL_CHATROOMCREATE_RE	=	220,
	PROTOCOL_CHATROOMJOIN_RE	=	224,
	PROTOCOL_CHATROOMLIST_RE	=	229,
	PROTOCOL_FRIENDEXTLIST	=	230,
	PROTOCOL_SENDAUMAIL_RE	=	234,
	PROTOCOL_TRADESTART_RE	=	4002,
	PROTOCOL_TRADEADDGOODS_RE	=	4005,
	PROTOCOL_TRADEREMOVEGOODS_RE	=	4007,
	PROTOCOL_TRADESUBMIT_RE	=	4011,
	PROTOCOL_TRADEMOVEOBJ_RE	=	4009,
	PROTOCOL_TRADECONFIRM_RE	=	4013,
	PROTOCOL_TRADEDISCARD_RE	=	4015,
	PROTOCOL_TRADEEND	=	4016,
	PROTOCOL_GMRESTARTSERVER_RE	=	359,
	PROTOCOL_GMONLINENUM_RE	=	351,
	PROTOCOL_GMLISTONLINEUSER_RE	=	353,
	PROTOCOL_GMKICKOUTUSER_RE	=	355,
	PROTOCOL_GMFORBIDSELLPOINT_RE	=	379,
	PROTOCOL_GMKICKOUTROLE_RE	=	361,
	PROTOCOL_GMSHUTUP_RE	=	357,
	PROTOCOL_GMSHUTUPROLE_RE	=	363,
	PROTOCOL_GMTOGGLECHAT_RE	=	365,
	PROTOCOL_GMFORBIDROLE_RE	=	367,
	PROTOCOL_REPORT2GM_RE	=	369,
	PROTOCOL_COMPLAIN2GM_RE	=	371,
	PROTOCOL_ANNOUNCEFORBIDINFO	=	123,
	PROTOCOL_SETMAXONLINENUM_RE	=	374,
	PROTOCOL_GMCONTROLGAME_RE	=	381,
	PROTOCOL_GMSETTIMELESSAUTOLOCK_RE	=	386,
	PROTOCOL_ACREMOTECODE	=	5003,
	PROTOCOL_ACQUESTION	=	5031,
	PROTOCOL_PLAYERACCUSE_RE	=	162,
	PROTOCOL_GETMAILLIST_RE	=	4203,
	PROTOCOL_GETMAIL_RE	=	4205,
	PROTOCOL_GETMAILATTACHOBJ_RE	=	4207,
	PROTOCOL_DELETEMAIL_RE	=	4209,
	PROTOCOL_PRESERVEMAIL_RE	=	4211,
	PROTOCOL_PLAYERSENDMAIL_RE	=	4213,
	PROTOCOL_AUCTIONOPEN_RE	=	801,
	PROTOCOL_AUCTIONBID_RE	=	803,
	PROTOCOL_AUCTIONCLOSE_RE	=	807,
	PROTOCOL_AUCTIONLIST_RE	=	805,
	PROTOCOL_AUCTIONGET_RE	=	809,
	PROTOCOL_AUCTIONGETITEM_RE	=	821,
	PROTOCOL_AUCTIONATTENDLIST_RE	=	817,
	PROTOCOL_AUCTIONEXITBID_RE	=	819,
	PROTOCOL_AUCTIONLISTUPDATE_RE	=	824,
	PROTOCOL_BATTLEGETMAP_RE	=	851,
	PROTOCOL_BATTLESTATUS_RE	=	867,
	PROTOCOL_BATTLECHALLENGE_RE	=	853,
	PROTOCOL_BATTLECHALLENGEMAP_RE	=	855,
	PROTOCOL_BATTLEENTER_RE	=	861,
	PROTOCOL_COUNTRYBATTLEMOVE_RE	=	4761,
	PROTOCOL_COUNTRYBATTLESYNCPLAYERLOCATION	=	4762,
	PROTOCOL_COUNTRYBATTLEGETMAP_RE	=	4768,
	PROTOCOL_COUNTRYBATTLEGETCONFIG_RE	=	4773,
	PROTOCOL_COUNTRYBATTLEGETSCORE_RE	=	4775,
	PROTOCOL_COUNTRYBATTLEPREENTERNOTIFY	=	4776,
	PROTOCOL_COUNTRYBATTLERESULT	=	4778,
	PROTOCOL_COUNTRYBATTLESINGLEBATTLERESULT	=	4780,
	PROTOCOL_COUNTRYBATTLEKINGASSIGNASSAULT_RE	=	4782,
	PROTOCOL_COUNTRYBATTLEGETBATTLELIMIT_RE	=	4785,
	PROTOCOL_COUNTRYBATTLEGETKINGCOMMANDPOINT_RE	=	4787,
	PROTOCOL_GETCNETSERVERCONFIG_RE	=	4789,
	PROTOCOL_HOMEBROWSEINFOQUERY_RE	=	5410,
	PROTOCOL_HOMEVISITORINFO	=	5398,
	PROTOCOL_HOMEEDITRES	=	5409,
	PROTOCOL_HOMEFOUNDRYFORMULASSYNC	=	5386,
	PROTOCOL_HOMESYNCNOTICECLIENT	=	5378,
	PROTOCOL_HOMEDETAIL	=	5358,
	PROTOCOL_HOMEBRIEF	=	5357,
	PROTOCOL_ANNOUNCESERVERATTRIBUTE	=	132,
	PROTOCOL_ANNOUNCECHALLENGEALGO	=	136,
	PROTOCOL_ANNOUNCEAUTHDVERSION	=	137,
	PROTOCOL_GETSELLLIST_RE	=	604,
	PROTOCOL_FINDSELLPOINTINFO_RE	=	620,
	PROTOCOL_ANNOUNCESELLRESULT	=	610,
	PROTOCOL_SELLCANCEL_RE	=	606,
	PROTOCOL_BUYPOINT_RE	=	608,
	PROTOCOL_STOCKCOMMISSION_RE	=	409,
	PROTOCOL_STOCKACCOUNT_RE	=	408,
	PROTOCOL_STOCKTRANSACTION_RE	=	410,
	PROTOCOL_STOCKBILL_RE	=	406,
	PROTOCOL_STOCKCANCEL_RE	=	412,
	PROTOCOL_CASHLOCK_RE	=	4261,
	PROTOCOL_CASHPASSWORDSET_RE	=	4264,
	PROTOCOL_AUTOLOCKSET_RE	=	783,
	PROTOCOL_FORWARDCHAT	=	8000,
	PROTOCOL_DISABLEAUTOLOCK	=	8007,
	PROTOCOL_REFWITHDRAWBONUS_RE	=	4907,
	PROTOCOL_REFLISTREFERRALS_RE	=	4905,
	PROTOCOL_REFGETREFERENCECODE_RE	=	4909,
	PROTOCOL_REWARDMATURENOTICE	=	4956,
	PROTOCOL_EXCHANGECONSUMEPOINTS_RE	=	4955,
	PROTOCOL_GETREWARDLIST_RE	=	4953,
	PROTOCOL_WEBTRADEPREPOST_RE	=	4303,
	PROTOCOL_WEBTRADEPRECANCELPOST_RE	=	4306,
	PROTOCOL_WEBTRADELIST_RE	=	4309,
	PROTOCOL_WEBTRADEGETITEM_RE	=	4311,
	PROTOCOL_WEBTRADEATTENDLIST_RE	=	4313,
	PROTOCOL_WEBTRADEGETDETAIL_RE	=	4315,
	PROTOCOL_WEBTRADEUPDATE_RE	=	4325,
	PROTOCOL_SYSAUCTIONLIST_RE	=	4352,
	PROTOCOL_SYSAUCTIONGETITEM_RE	=	4354,
	PROTOCOL_SYSAUCTIONACCOUNT_RE	=	4356,
	PROTOCOL_SYSAUCTIONBID_RE	=	4358,
	PROTOCOL_SYSAUCTIONCASHTRANSFER_RE	=	4360,
	PROTOCOL_CREATEFACTIONFORTRESS_RE	=	4407,
	PROTOCOL_FACTIONFORTRESSLIST_RE	=	4415,
	PROTOCOL_FACTIONFORTRESSCHALLENGE_RE	=	4417,
	PROTOCOL_FACTIONFORTRESSBATTLELIST_RE	=	4420,
	PROTOCOL_FACTIONFORTRESSGET_RE	=	4422,
	PROTOCOL_SWITCHSERVERSTART	=	4101,
	PROTOCOL_USERCOUPON_RE	=	139,
	PROTOCOL_USERCOUPONEXCHANGE_RE	=	141,
	PROTOCOL_USERADDCASH_RE	=	145,
	PROTOCOL_SSOGETTICKET_RE	=	148,
	PROTOCOL_QPANNOUNCEDISCOUNT	=	149,
	PROTOCOL_QPGETACTIVATEDSERVICES_RE	=	151,
	PROTOCOL_QPADDCASH_RE	=	153,
	PROTOCOL_PLAYERCHANGEDS_RE	=	1103,
	PROTOCOL_CHANGEDS_RE	=	1104,
	PROTOCOL_KICKOUTUSER2	=	1126,
	PROTOCOL_KEGETSTATUS_RE	=	4853,
	PROTOCOL_KECANDIDATEAPPLY_RE	=	4855,
	PROTOCOL_KEVOTING_RE	=	4859,
	PROTOCOL_PSHOPCREATE_RE	=	901,
	PROTOCOL_PSHOPBUY_RE	=	903,
	PROTOCOL_PSHOPSELL_RE	=	905,
	PROTOCOL_PSHOPCANCELGOODS_RE	=	907,
	PROTOCOL_PSHOPPLAYERBUY_RE	=	909,
	PROTOCOL_PSHOPPLAYERSELL_RE	=	911,
	PROTOCOL_PSHOPSETTYPE_RE	=	913,
	PROTOCOL_PSHOPACTIVE_RE	=	915,
	PROTOCOL_PSHOPMANAGEFUND_RE	=	917,
	PROTOCOL_PSHOPDRAWITEM_RE	=	919,
	PROTOCOL_PSHOPCLEARGOODS_RE	=	921,
	PROTOCOL_PSHOPSELFGET_RE	=	923,
	PROTOCOL_PSHOPPLAYERGET_RE	=	925,
	PROTOCOL_PSHOPLIST_RE	=	927,
	PROTOCOL_PSHOPLISTITEM_RE	=	929,
	PROTOCOL_PLAYERPROFILEGETPROFILEDATA_RE	=	952,
	PROTOCOL_PLAYERPROFILEGETMATCHRESULT_RE	=	955,
	PROTOCOL_AUTOTEAMSETGOAL_RE	=	963,
	PROTOCOL_AUTOTEAMPLAYERLEAVE	=	968,
	PROTOCOL_FACTIONRESOURCEBATTLEPLAYERQUERYRESULT	=	4431,
	PROTOCOL_FACTIONRESOURCEBATTLEGETMAP_RE	=	4435,
	PROTOCOL_FACTIONRESOURCEBATTLEGETRECORD_RE	=	4437,
	PROTOCOL_FACTIONRESOURCEBATTLENOTIFYPLAYEREVENT	=	4438,
	PROTOCOL_MNGETFACTIONINFO_RE	=	5249,
	PROTOCOL_EC_ARENAPLAYERTOPLISTQUERY_RE	=	5659,
	PROTOCOL_EC_ARENATEAMTOPLISTQUERY_RE	=	5661,
	PROTOCOL_EC_ARENATEAMTOPLISTDETAILQUERY_RE	=	5663,
	PROTOCOL_EC_ARENAQUERY_RE	=	5622,
	PROTOCOL_EC_ARENAPLAYERTOTALINFOQUERY_RE	=	5665,
	PROTOCOL_EC_ARENATEAMDATANOTIFY	=	5623,
	PROTOCOL_EC_ARENAPLAYERDATANOTIFY	=	5624,
	PROTOCOL_EC_ARENATEAMCREATE	=	5591,
	PROTOCOL_EC_ARENABATTLEINFONOTIFYCLIENT	=	5612,
	PROTOCOL_EC_ARENACLIENTNOTIFY	=	5589,
	PROTOCOL_EC_ARENATEAMMATCH	=	5603,
	PROTOCOL_EC_ARENAMATCHCANCELNOTIFY	=	5648,
	PROTOCOL_EC_ARENAONLINEINFONOTIFY	=	5681,
	PROTOCOL_GCODEXREQUESTSTORAGE	=	13100,
	PROTOCOL_GCODEXREQUESTSTORAGE_RE	=	6181,
	PROTOCOL_ANNOUNCEPROVIDERID	=	73,
	PROTOCOL_S2CGAMEDATASEND	=	74,
	PROTOCOL_S2CMULTICAST	=	77,
	PROTOCOL_S2CBROADCAST	=	78,
	PROTOCOL_PLAYERHEARTBEAT	=	93,
	PROTOCOL_SWITCHSERVERCANCEL	=	4102,
	PROTOCOL_SWITCHSERVERSUCCESS	=	4103,
	PROTOCOL_SWITCHSERVERTIMEOUT	=	4104,
	PROTOCOL_FACTIONOPREQUEST_RE	=	4805,
	PROTOCOL_FACTIONCREATE_RE	=	4502,
	PROTOCOL_FACTIONAPPLYJOIN_RE	=	4504,
	PROTOCOL_FACTIONLISTMEMBER_RE	=	4503,
	PROTOCOL_FACTIONACCEPTJOIN_RE	=	4505,
	PROTOCOL_FACTIONEXPEL_RE	=	4506,
	PROTOCOL_FACTIONBROADCASTNOTICE_RE	=	4507,
	PROTOCOL_FACTIONMASTERRESIGN_RE	=	4509,
	PROTOCOL_FACTIONAPPOINT_RE	=	4510,
	PROTOCOL_FACTIONRESIGN_RE	=	4511,
	PROTOCOL_FACTIONCHANGPROCLAIM_RE	=	4508,
	PROTOCOL_FACTIONLEAVE_RE	=	4512,
	PROTOCOL_FACTIONDISMISS_RE	=	4515,
	PROTOCOL_FACTIONRENAME_RE	=	4516,
	PROTOCOL_FACTIONUPGRADE_RE	=	4513,
	PROTOCOL_FACTIONDEGRADE_RE	=	4514,
	PROTOCOL_FACTIONALLIANCEAPPLY_RE	=	4517,
	PROTOCOL_FACTIONALLIANCEREPLY_RE	=	4518,
	PROTOCOL_FACTIONHOSTILEAPPLY_RE	=	4519,
	PROTOCOL_FACTIONHOSTILEREPLY_RE	=	4520,
	PROTOCOL_FACTIONREMOVERELATIONAPPLY_RE	=	4521,
	PROTOCOL_FACTIONREMOVERELATIONREPLY_RE	=	4522,
	PROTOCOL_FACTIONLISTRELATION_RE	=	4523,
	PROTOCOL_FACTIONRELATIONRECVAPPLY	=	4524,
	PROTOCOL_FACTIONRELATIONRECVREPLY	=	4525,
	PROTOCOL_FACTIONDELAYEXPELANNOUNCE	=	4526,
	PROTOCOL_GETFACTIONBASEINFO_RE	=	4815,
	PROTOCOL_GETPLAYERFACTIONINFO_RE	=	4817,
	PROTOCOL_FACTIONLISTONLINE_RE	=	4820,
	PROTOCOL_FACTIONRENAMEANNOUNCE	=	4532,
};

};
#endif
